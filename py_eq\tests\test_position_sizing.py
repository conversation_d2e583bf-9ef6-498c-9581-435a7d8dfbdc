"""
Test position sizing calculation fixes
Tests the new formula: quantity = available_balance * 3.5 / share_price
with constraint: quantity * risk <= 1% of morning balance
"""
import pytest
import logging
from unittest.mock import Mock, MagicMock
import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from services.real_balance_service import RealBalanceService
from services.interfaces import OrderServiceInterface


class MockOrderService(OrderServiceInterface):
    def __init__(self, balance=10000.0):
        self.balance = balance
        
    def get_real_balance(self):
        return {
            'available_balance': self.balance,
            'total_balance': self.balance * 1.2,
            'margin_used': 0.0
        }
        
    def place_order(self, symbol, token, exchange, transaction_type, entry_price, stop_loss, target, quantity):
        return None
        
    def get_order_status(self, order_id: str):
        return None
        
    def square_off_position(self, order_id: str, exit_price: float):
        return True


@pytest.fixture
def mock_logger():
    return Mock(spec=logging.Logger)


@pytest.fixture
def balance_service(mock_logger):
    order_service = MockOrderService(balance=10000.0)  # ₹10,000 available balance
    return RealBalanceService(
        order_service=order_service,
        logger=mock_logger
    )


class TestPositionSizingFormula:
    """Test the new position sizing formula"""
    
    def test_basic_position_sizing_calculation(self, balance_service):
        """Test basic position sizing using available_balance * 3.5 / share_price"""
        # Available balance: ₹10,000
        # Formula: quantity = 10,000 * 3.5 / 100 = 350 shares
        entry_price = 100.0
        stop_loss = 95.0
        
        quantity = balance_service.calculate_position_size(entry_price, stop_loss)
        
        expected_quantity = int((10000 * 3.5) / 100)  # 350 shares
        assert quantity == expected_quantity
        
    def test_position_sizing_with_higher_price(self, balance_service):
        """Test position sizing with higher share price"""
        # Available balance: ₹10,000
        # Formula: quantity = 10,000 * 3.5 / 2500 = 14 shares
        entry_price = 2500.0
        stop_loss = 2400.0
        
        quantity = balance_service.calculate_position_size(entry_price, stop_loss)
        
        expected_quantity = int((10000 * 3.5) / 2500)  # 14 shares
        assert quantity == expected_quantity
        
    def test_position_sizing_with_fractional_result(self, balance_service):
        """Test position sizing when formula gives fractional shares"""
        # Available balance: ₹10,000
        # Formula: quantity = 10,000 * 3.5 / 333 = 105.1... → 105 shares
        entry_price = 333.0
        stop_loss = 320.0
        
        quantity = balance_service.calculate_position_size(entry_price, stop_loss)
        
        expected_quantity = int((10000 * 3.5) / 333)  # 105 shares
        assert quantity == expected_quantity
        
    def test_minimum_quantity_enforcement(self, balance_service):
        """Test that minimum quantity of 1 is enforced"""
        # Very high price that would result in 0 shares
        entry_price = 50000.0  # ₹50,000 per share
        stop_loss = 48000.0
        
        quantity = balance_service.calculate_position_size(entry_price, stop_loss)
        
        # Should return at least 1 share
        assert quantity >= 1


class TestRiskConstraint:
    """Test the risk constraint: quantity * risk <= 1% of morning balance"""
    
    def test_risk_constraint_applied(self, balance_service):
        """Test that risk constraint reduces position size when needed"""
        # Available balance: ₹10,000
        # Daily risk amount (1% of morning balance): ₹100
        # Entry: ₹100, SL: ₹50, Risk per share: ₹50
        # Formula quantity: 10,000 * 3.5 / 100 = 350 shares
        # Risk for 350 shares: 350 * 50 = ₹17,500 (exceeds ₹100 limit)
        # Risk-limited quantity: 100 / 50 = 2 shares
        
        entry_price = 100.0
        stop_loss = 50.0  # High risk per share
        
        quantity = balance_service.calculate_position_size(entry_price, stop_loss)
        
        # Should be limited by risk constraint
        daily_risk = balance_service.get_daily_risk_per_trade()  # 1% of morning balance
        risk_per_share = abs(entry_price - stop_loss)
        max_quantity_by_risk = int(daily_risk / risk_per_share)
        
        assert quantity == max_quantity_by_risk
        assert quantity * risk_per_share <= daily_risk
        
    def test_risk_constraint_not_applied_when_not_needed(self, balance_service):
        """Test that risk constraint doesn't interfere when not needed"""
        # Available balance: ₹10,000
        # Daily risk amount: ₹100
        # Entry: ₹100, SL: ₹99, Risk per share: ₹1
        # Formula quantity: 10,000 * 3.5 / 100 = 350 shares
        # Risk for 350 shares: 350 * 1 = ₹350 (exceeds ₹100 limit)
        # Risk-limited quantity: 100 / 1 = 100 shares
        
        entry_price = 100.0
        stop_loss = 99.0  # Low risk per share
        
        quantity = balance_service.calculate_position_size(entry_price, stop_loss)
        
        # Should be limited by risk constraint to 100 shares
        daily_risk = balance_service.get_daily_risk_per_trade()
        risk_per_share = abs(entry_price - stop_loss)
        expected_quantity = int(daily_risk / risk_per_share)
        
        assert quantity == expected_quantity
        
    def test_very_low_risk_scenario(self, balance_service):
        """Test scenario where risk constraint allows full formula quantity"""
        # This is a theoretical test - in practice, risk constraint usually applies
        # Available balance: ₹10,000
        # Entry: ₹1000, SL: ₹999.90, Risk per share: ₹0.10
        # Formula quantity: 10,000 * 3.5 / 1000 = 35 shares
        # Risk for 35 shares: 35 * 0.10 = ₹3.50 (well under ₹100 limit)
        
        entry_price = 1000.0
        stop_loss = 999.90  # Very tight stop loss
        
        quantity = balance_service.calculate_position_size(entry_price, stop_loss)
        
        formula_quantity = int((10000 * 3.5) / 1000)  # 35 shares
        risk_per_share = abs(entry_price - stop_loss)
        total_risk = quantity * risk_per_share
        daily_risk = balance_service.get_daily_risk_per_trade()
        
        # Risk should be well under limit
        assert total_risk <= daily_risk
        assert quantity == formula_quantity


class TestEdgeCases:
    """Test edge cases and error conditions"""
    
    def test_zero_entry_price(self, balance_service):
        """Test handling of zero entry price"""
        quantity = balance_service.calculate_position_size(0.0, 95.0)
        assert quantity == 0
        
    def test_negative_entry_price(self, balance_service):
        """Test handling of negative entry price"""
        quantity = balance_service.calculate_position_size(-100.0, 95.0)
        assert quantity == 0
        
    def test_zero_stop_loss(self, balance_service):
        """Test handling of zero stop loss"""
        quantity = balance_service.calculate_position_size(100.0, 0.0)
        assert quantity == 0
        
    def test_negative_stop_loss(self, balance_service):
        """Test handling of negative stop loss"""
        quantity = balance_service.calculate_position_size(100.0, -95.0)
        # Should still work as we use abs(entry_price - stop_loss)
        expected_quantity = int((10000 * 3.5) / 100)
        risk_per_share = abs(100.0 - (-95.0))  # 195
        daily_risk = balance_service.get_daily_risk_per_trade()
        risk_limited_quantity = int(daily_risk / risk_per_share)
        
        assert quantity == min(expected_quantity, risk_limited_quantity)
        
    def test_entry_price_equals_stop_loss(self, balance_service):
        """Test handling when entry price equals stop loss (zero risk)"""
        quantity = balance_service.calculate_position_size(100.0, 100.0)
        assert quantity == 0


class TestDifferentBalanceScenarios:
    """Test position sizing with different balance amounts"""
    
    def test_low_balance_scenario(self, mock_logger):
        """Test position sizing with low available balance"""
        order_service = MockOrderService(balance=1000.0)  # ₹1,000 only
        balance_service = RealBalanceService(
            order_service=order_service,
            logger=mock_logger
        )
        
        entry_price = 100.0
        stop_loss = 95.0
        
        quantity = balance_service.calculate_position_size(entry_price, stop_loss)
        
        expected_quantity = int((1000 * 3.5) / 100)  # 35 shares
        daily_risk = balance_service.get_daily_risk_per_trade()  # 1% of morning balance
        risk_per_share = abs(entry_price - stop_loss)
        risk_limited_quantity = int(daily_risk / risk_per_share)
        
        assert quantity == min(expected_quantity, risk_limited_quantity)
        
    def test_high_balance_scenario(self, mock_logger):
        """Test position sizing with high available balance"""
        order_service = MockOrderService(balance=100000.0)  # ₹1,00,000
        balance_service = RealBalanceService(
            order_service=order_service,
            logger=mock_logger
        )
        
        entry_price = 100.0
        stop_loss = 95.0
        
        quantity = balance_service.calculate_position_size(entry_price, stop_loss)
        
        expected_quantity = int((100000 * 3.5) / 100)  # 3,500 shares
        daily_risk = balance_service.get_daily_risk_per_trade()
        risk_per_share = abs(entry_price - stop_loss)
        risk_limited_quantity = int(daily_risk / risk_per_share)
        
        # With high balance, risk constraint will likely apply
        assert quantity == min(expected_quantity, risk_limited_quantity)
        assert quantity * risk_per_share <= daily_risk


class TestMaxOrderValue:
    """Test the max order value calculation"""
    
    def test_max_order_value_calculation(self, balance_service):
        """Test that max order value is available_balance * 3.5"""
        max_order_value = balance_service.get_max_order_value()
        expected_max = 10000 * 3.5  # ₹35,000
        assert max_order_value == expected_max
        
    def test_order_validation_against_max_value(self, balance_service):
        """Test order validation against max order value"""
        # Order value that exceeds max
        order_value = 40000.0  # Exceeds ₹35,000 limit
        estimated_costs = 100.0
        
        is_valid = balance_service.validate_order_value(order_value, estimated_costs)
        assert not is_valid
        
        # Order value within limit
        order_value = 30000.0  # Within ₹35,000 limit
        is_valid = balance_service.validate_order_value(order_value, estimated_costs)
        assert is_valid


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
