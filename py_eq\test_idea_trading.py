#!/usr/bin/env python3
"""
Comprehensive test script for IDEA-EQ trading
Tests the enhanced order service with tradeBook integration for accurate price retrieval
"""

import os
import sys
import time
import logging
from datetime import datetime
from typing import Optional

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.order import Order, TransactionType, OrderType, ProductType, OrderStatus
from services.order_service import SmartAPIOrderService
from config.config import config
from utils.safe_logging import safe_info, safe_error

class IDEATradingTest:
    """Test class for IDEA-EQ trading with enhanced price retrieval"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.setup_logging()
        self.order_service = None
        self.test_results = {}
        
    def setup_logging(self):
        """Setup detailed logging for the test"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'test_idea_trading_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
                logging.StreamHandler()
            ]
        )
        
    def initialize_order_service(self) -> bool:
        """Initialize the SmartAPI order service"""
        try:
            self.logger.info("🚀 Initializing SmartAPI Order Service...")
            self.order_service = SmartAPIOrderService(config)
            
            # Test authentication
            if self.order_service.authenticate():
                self.logger.info("✅ SmartAPI authentication successful")
                return True
            else:
                self.logger.error("❌ SmartAPI authentication failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error initializing order service: {e}")
            return False
    
    def get_idea_symbol_info(self) -> Optional[tuple]:
        """Get IDEA symbol token and exchange information"""
        try:
            self.logger.info("🔍 Getting IDEA symbol information...")
            
            # Try to get symbol token for IDEA
            symbol_token = self.order_service._get_symbol_token("IDEA")
            if symbol_token:
                self.logger.info(f"✅ Found IDEA token: {symbol_token}")
                return symbol_token, "NSE"
            else:
                self.logger.error("❌ Could not find IDEA symbol token")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error getting IDEA symbol info: {e}")
            return None
    
    def get_current_idea_price(self) -> Optional[float]:
        """Get current market price for IDEA"""
        try:
            self.logger.info("💰 Getting current IDEA market price...")
            
            price = self.order_service._get_current_market_price("IDEA")
            if price and price > 0:
                self.logger.info(f"✅ Current IDEA price: ₹{price:.2f}")
                return price
            else:
                self.logger.error("❌ Could not get current IDEA price")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error getting IDEA price: {e}")
            return None
    
    def calculate_sl_target(self, entry_price: float, transaction_type: TransactionType) -> tuple:
        """Calculate stop loss and target prices"""
        # Use 2% SL and 4% target for testing (2:1 risk-reward)
        sl_percentage = 0.02  # 2% stop loss
        target_percentage = 0.04  # 4% target
        
        if transaction_type == TransactionType.BUY:
            stop_loss = entry_price * (1 - sl_percentage)
            target = entry_price * (1 + target_percentage)
        else:
            stop_loss = entry_price * (1 + sl_percentage)
            target = entry_price * (1 - target_percentage)
            
        return stop_loss, target
    
    def test_idea_buy_order(self) -> bool:
        """Test placing a BUY order for 5 quantity of IDEA-EQ"""
        try:
            self.logger.info("📈 Testing IDEA BUY order placement...")
            
            # Get symbol info
            symbol_info = self.get_idea_symbol_info()
            if not symbol_info:
                return False
            
            symbol_token, exchange = symbol_info
            
            # Get current price
            current_price = self.get_current_idea_price()
            if not current_price:
                return False
            
            # Calculate SL and target
            stop_loss, target = self.calculate_sl_target(current_price, TransactionType.BUY)
            
            self.logger.info(f"📊 Order parameters:")
            self.logger.info(f"   Symbol: IDEA-EQ")
            self.logger.info(f"   Quantity: 5")
            self.logger.info(f"   Type: BUY (Market Order)")
            self.logger.info(f"   Entry Price: ₹{current_price:.2f}")
            self.logger.info(f"   Stop Loss: ₹{stop_loss:.2f}")
            self.logger.info(f"   Target: ₹{target:.2f}")
            
            # Place the order
            order = self.order_service.place_order(
                symbol="IDEA",
                token=symbol_token,
                exchange=exchange,
                transaction_type=TransactionType.BUY,
                entry_price=current_price,
                stop_loss=stop_loss,
                target=target,
                quantity=5
            )
            
            if order:
                self.logger.info(f"✅ Order placed successfully!")
                self.logger.info(f"   Order ID: {order.order_id}")
                self.logger.info(f"   Status: {order.status}")
                
                # Store test results
                self.test_results['buy_order'] = {
                    'success': True,
                    'order_id': order.order_id,
                    'original_price': current_price,
                    'executed_price': order.entry_price,
                    'stop_loss': order.stop_loss,
                    'target': order.target,
                    'quantity': order.quantity
                }
                
                # Test price retrieval after a delay
                self.logger.info("⏳ Waiting 10 seconds before testing price retrieval...")
                time.sleep(10)
                
                # Test comprehensive price lookup
                retrieved_price = self.order_service._get_executed_price_comprehensive(
                    order.order_id, "IDEA", max_retries=3
                )
                
                if retrieved_price:
                    self.logger.info(f"✅ Price retrieval test successful: ₹{retrieved_price:.2f}")
                    self.test_results['buy_order']['price_retrieval_success'] = True
                    self.test_results['buy_order']['retrieved_price'] = retrieved_price
                else:
                    self.logger.warning("⚠️ Price retrieval test failed")
                    self.test_results['buy_order']['price_retrieval_success'] = False
                
                return True
            else:
                self.logger.error("❌ Order placement failed")
                self.test_results['buy_order'] = {'success': False, 'error': 'Order placement failed'}
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error in BUY order test: {e}")
            self.test_results['buy_order'] = {'success': False, 'error': str(e)}
            return False
    
    def test_position_retrieval(self) -> bool:
        """Test retrieving positions and checking SL/target calculations"""
        try:
            self.logger.info("📋 Testing position retrieval...")
            
            positions = self.order_service.get_open_positions()
            
            if positions:
                self.logger.info(f"✅ Retrieved {len(positions)} positions")
                
                # Look for IDEA position
                idea_position = None
                for pos in positions:
                    if pos.symbol == "IDEA":
                        idea_position = pos
                        break
                
                if idea_position:
                    self.logger.info(f"✅ Found IDEA position:")
                    self.logger.info(f"   Entry Price: ₹{idea_position.entry_price:.2f}")
                    self.logger.info(f"   Stop Loss: ₹{idea_position.stop_loss:.2f}")
                    self.logger.info(f"   Target: ₹{idea_position.target:.2f}")
                    self.logger.info(f"   Quantity: {idea_position.quantity}")
                    
                    self.test_results['position_retrieval'] = {
                        'success': True,
                        'found_idea': True,
                        'entry_price': idea_position.entry_price,
                        'stop_loss': idea_position.stop_loss,
                        'target': idea_position.target
                    }
                    return True
                else:
                    self.logger.warning("⚠️ IDEA position not found in retrieved positions")
                    self.test_results['position_retrieval'] = {
                        'success': True,
                        'found_idea': False
                    }
                    return False
            else:
                self.logger.info("ℹ️ No open positions found")
                self.test_results['position_retrieval'] = {
                    'success': True,
                    'found_idea': False
                }
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error in position retrieval test: {e}")
            self.test_results['position_retrieval'] = {'success': False, 'error': str(e)}
            return False
    
    def print_test_summary(self):
        """Print a comprehensive test summary"""
        self.logger.info("=" * 60)
        self.logger.info("📊 IDEA-EQ TRADING TEST SUMMARY")
        self.logger.info("=" * 60)
        
        for test_name, results in self.test_results.items():
            self.logger.info(f"\n🧪 {test_name.upper()}:")
            if results.get('success'):
                self.logger.info("   ✅ PASSED")
                for key, value in results.items():
                    if key != 'success':
                        self.logger.info(f"   {key}: {value}")
            else:
                self.logger.info("   ❌ FAILED")
                if 'error' in results:
                    self.logger.info(f"   Error: {results['error']}")
        
        self.logger.info("\n" + "=" * 60)
    
    def run_comprehensive_test(self):
        """Run the complete test suite"""
        self.logger.info("🚀 Starting IDEA-EQ Trading Test Suite")
        self.logger.info("=" * 60)
        
        # Initialize order service
        if not self.initialize_order_service():
            self.logger.error("❌ Failed to initialize order service. Exiting.")
            return False
        
        # Test BUY order
        self.test_idea_buy_order()
        
        # Wait a bit before testing position retrieval
        time.sleep(5)
        
        # Test position retrieval
        self.test_position_retrieval()
        
        # Print summary
        self.print_test_summary()
        
        return True

def main():
    """Main function to run the test"""
    test = IDEATradingTest()
    
    try:
        test.run_comprehensive_test()
    except KeyboardInterrupt:
        test.logger.info("🛑 Test interrupted by user")
    except Exception as e:
        test.logger.error(f"❌ Unexpected error: {e}")
    finally:
        test.logger.info("🏁 Test completed")

if __name__ == "__main__":
    main()
