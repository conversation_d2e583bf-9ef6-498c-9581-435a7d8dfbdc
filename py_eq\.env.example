# =============================================================================
# TRADING MODE CONFIGURATION
# =============================================================================

# Paper Trading (Virtual Money) - DEFAULT - SAFE FOR TESTING
PAPER_TRADING=true

# Real Trading (Live Money) - CAUTION: Uses real money from your Angel One account!
# Uncomment the line below to enable real trading
# REAL_TRADING=true

# Initial balance for paper trading (per strategy)
INITIAL_BALANCE=80000.0

# =============================================================================
# SMARTAPI CONFIGURATION (Required for Real Trading)
# =============================================================================

# Get these from Angel One SmartAPI portal: https://smartapi.angelone.in/
# Required for real trading, optional for paper trading
SMARTAPI_API_KEY=your_api_key_here
SMARTAPI_USERNAME=your_client_code_here
SMARTAPI_PASSWORD=your_pin_here
SMARTAPI_TOTP_TOKEN=your_totp_secret_here

# =============================================================================
# TRADING PARAMETERS
# =============================================================================

# Maximum trades per day (across all strategies)
MAX_TRADES_PER_DAY=3

# Maximum risk per trade in rupees
MAX_RISK_PER_TRADE=800.0

# Margin multiplier for intraday trading
MARGIN_MULTIPLIER=4

# Time to square off all positions (HH:MM format)
SQUARE_OFF_TIME=15:12

# Risk-reward ratio
RISK_REWARD_RATIO=3.0

# Stop loss buffer percentage
STOP_LOSS_BUFFER=0.002

# Technical Indicators
EMA_FAST_PERIOD=9
EMA_SLOW_PERIOD=20
EMA_TREND_PERIOD=50
RSI_PERIOD=14
VOLUME_PERIOD=10
RSI_OVERSOLD=30.0
RSI_OVERBOUGHT=70.0
RSI_NEUTRAL=50.0

# MongoDB Configuration (optional)
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/
MONGODB_DATABASE_NAME=trading_db

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=trading.log

# Market Data Service Configuration
# Options: simulated, real, hybrid
# simulated: Use simulated data for testing
# real: Use SmartAPI live data only
# hybrid: Combine MongoDB historical data with SmartAPI live data + YFinance gap filling
MARKET_DATA_SERVICE=hybrid

# Market Override (for testing)
OVERRIDE_MARKET_OPEN=false
OVERRIDE_ENTRY_WINDOW=false

# =============================================================================
# SAFETY NOTES FOR REAL TRADING
# =============================================================================

# ⚠️  IMPORTANT: Real trading uses actual money from your Angel One account!
#
# Before enabling real trading:
# 1. Test thoroughly with paper trading first
# 2. Ensure you have sufficient balance in your Angel One account
# 3. Start with small amounts when switching to real trading
# 4. Monitor your trades closely during market hours
# 5. Set appropriate stop losses and risk management
# 6. Keep your SmartAPI credentials secure and never share them
#
# To enable real trading:
# 1. Get SmartAPI credentials from https://smartapi.angelone.in/
# 2. Update SMARTAPI_* variables above with your actual credentials
# 3. Set REAL_TRADING=true (or PAPER_TRADING=false)
# 4. Test with small amounts first
# 5. Monitor your account balance and positions regularly

# =============================================================================
# EXAMPLE REAL TRADING CONFIGURATION
# =============================================================================

# REAL_TRADING=true
# SMARTAPI_API_KEY=AbCdEf123456
# SMARTAPI_USERNAME=D12345
# SMARTAPI_PASSWORD=1234
# SMARTAPI_TOTP_TOKEN=ABCDEFGHIJKLMNOPQRSTUVWXYZ234567
# MAX_TRADES_PER_DAY=2
# MAX_RISK_PER_TRADE=500.0
