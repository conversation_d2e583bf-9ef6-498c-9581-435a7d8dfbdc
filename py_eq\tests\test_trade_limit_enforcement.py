"""
Test trade limit enforcement to prevent excessive order placement
Critical test to ensure max 3 trades per day limit is strictly enforced
"""
import pytest
import logging
from unittest.mock import Mock, MagicMock, patch
from datetime import datetime, time
import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from strategies.production_strategy_manager import ProductionStrategyManager, TradeTracker
from models.order import Order, TransactionType, OrderType, ProductType, OrderStatus
from models.signal import Signal, SignalType
from services.interfaces import MarketDataServiceInterface, OrderServiceInterface
from config.trading_config import TradingConfig


class MockMarketDataService(MarketDataServiceInterface):
    def __init__(self):
        self.prices = {"RELIANCE": 2500.0, "TCS": 3500.0, "INFY": 1800.0}
        
    def get_current_price(self, symbol: str) -> float:
        return self.prices.get(symbol, 100.0)
        
    def get_symbol_token(self, symbol: str):
        return ("12345", "NSE")
        
    def get_historical_data(self, symbol: str, timeframe: str, days: int):
        return []


class MockOrderService(OrderServiceInterface):
    def __init__(self):
        self.orders_placed = []
        self.should_fail = False
        
    def place_order(self, symbol, token, exchange, transaction_type, entry_price, stop_loss, target, quantity):
        if self.should_fail:
            return None
            
        order = Order(
            order_id=f"ORDER_{len(self.orders_placed) + 1}",
            symbol=symbol,
            symbol_token=token,
            exchange=exchange,
            transaction_type=transaction_type,
            order_type=OrderType.MARKET,
            product_type=ProductType.INTRADAY,
            quantity=quantity,
            price=entry_price,
            stop_loss=stop_loss,
            target=target,
            status=OrderStatus.COMPLETED,
            entry_price=entry_price
        )
        self.orders_placed.append(order)
        return order
        
    def get_order_status(self, order_id: str):
        return OrderStatus.COMPLETED
        
    def square_off_position(self, order_id: str, exit_price: float):
        return True


@pytest.fixture
def mock_config():
    config = TradingConfig()
    config.max_trades_per_day = 3
    config.max_risk_per_trade = 1000.0
    return config


@pytest.fixture
def mock_logger():
    return Mock(spec=logging.Logger)


@pytest.fixture
def strategy_manager(mock_config, mock_logger):
    market_data_service = MockMarketDataService()
    order_service = MockOrderService()
    
    return ProductionStrategyManager(
        logger=mock_logger,
        market_data_service=market_data_service,
        order_service=order_service,
        config=mock_config,
        initial_trade_count=0,
        initial_traded_symbols=set()
    )


def create_test_signal(symbol="RELIANCE", signal_type=SignalType.BUY_STRONG):
    return Signal(
        symbol=symbol,
        signal_type=signal_type,
        strength=0.8,
        price=2500.0,
        stop_loss=2400.0,
        target=2600.0,
        strategy="TEST_STRATEGY"
    )


class TestTradeTracker:
    """Test the TradeTracker class that enforces trade limits"""
    
    def test_trade_tracker_initialization(self):
        """Test TradeTracker initializes with correct limits"""
        tracker = TradeTracker()
        assert tracker.total_trades_today == 0
        assert tracker.max_trades_per_day == 3
        assert tracker.ABSOLUTE_MAX_TRADES == 3
        assert tracker.can_trade() == True
        
    def test_trade_tracker_records_trades(self):
        """Test TradeTracker correctly records trades"""
        tracker = TradeTracker()
        
        # Record first trade
        tracker.record_trade("STRATEGY_1", "RELIANCE")
        assert tracker.total_trades_today == 1
        assert tracker.can_trade() == True
        assert "RELIANCE" in tracker.traded_symbols
        
        # Record second trade
        tracker.record_trade("STRATEGY_1", "TCS")
        assert tracker.total_trades_today == 2
        assert tracker.can_trade() == True
        
        # Record third trade (should reach limit)
        tracker.record_trade("STRATEGY_2", "INFY")
        assert tracker.total_trades_today == 3
        assert tracker.can_trade() == False
        
    def test_trade_tracker_prevents_exceeding_limit(self):
        """Test TradeTracker prevents exceeding absolute maximum"""
        tracker = TradeTracker()
        
        # Fill up to limit
        for i in range(3):
            tracker.record_trade("STRATEGY_1", f"STOCK_{i}")
            
        # Try to exceed limit - should raise exception
        with pytest.raises(ValueError, match="Cannot exceed 3 trades per day"):
            tracker.record_trade("STRATEGY_1", "EXCESS_STOCK")
            
    def test_trade_tracker_symbol_cooldown(self):
        """Test symbol cooldown prevents immediate re-trading"""
        tracker = TradeTracker()
        
        # Record trade for RELIANCE
        tracker.record_trade("STRATEGY_1", "RELIANCE")
        
        # Should not be able to trade same symbol immediately
        assert not tracker.can_trade_symbol("RELIANCE")
        
        # Should be able to trade different symbol
        assert tracker.can_trade_symbol("TCS")


class TestProductionStrategyManager:
    """Test ProductionStrategyManager trade limit enforcement"""
    
    def test_strategy_manager_respects_trade_limit(self, strategy_manager):
        """Test strategy manager stops at 3 trades"""
        signals = [
            create_test_signal("RELIANCE"),
            create_test_signal("TCS"),
            create_test_signal("INFY"),
            create_test_signal("HDFC"),  # This should be rejected
            create_test_signal("ICICIBANK"),  # This should be rejected
        ]
        
        # Process signals
        executed_orders = strategy_manager.process_signals(signals)
        
        # Should only execute 3 orders
        assert len(executed_orders) == 3
        assert strategy_manager.trade_tracker.total_trades_today == 3
        assert not strategy_manager.trade_tracker.can_trade()
        
    def test_strategy_manager_prevents_duplicate_symbols(self, strategy_manager):
        """Test strategy manager prevents trading same symbol twice"""
        signals = [
            create_test_signal("RELIANCE"),
            create_test_signal("RELIANCE"),  # Duplicate - should be rejected
            create_test_signal("TCS"),
        ]
        
        executed_orders = strategy_manager.process_signals(signals)
        
        # Should only execute 2 orders (RELIANCE once, TCS once)
        assert len(executed_orders) == 2
        symbols_traded = [order.symbol for order in executed_orders]
        assert "RELIANCE" in symbols_traded
        assert "TCS" in symbols_traded
        assert symbols_traded.count("RELIANCE") == 1  # Only once
        
    def test_strategy_manager_hard_limit_check(self, strategy_manager):
        """Test the hard-coded 3 trade limit in process_signals"""
        # Manually set trade count to 3
        strategy_manager.trade_tracker.total_trades_today = 3
        
        signals = [create_test_signal("RELIANCE")]
        executed_orders = strategy_manager.process_signals(signals)
        
        # Should return empty list due to hard limit
        assert len(executed_orders) == 0
        
    def test_strategy_manager_with_initial_trades(self, mock_config, mock_logger):
        """Test strategy manager with existing trades from logs"""
        market_data_service = MockMarketDataService()
        order_service = MockOrderService()
        
        # Initialize with 2 existing trades
        strategy_manager = ProductionStrategyManager(
            logger=mock_logger,
            market_data_service=market_data_service,
            order_service=order_service,
            config=mock_config,
            initial_trade_count=2,  # Already 2 trades today
            initial_traded_symbols={"RELIANCE", "TCS"}
        )
        
        signals = [
            create_test_signal("INFY"),  # Should be allowed (3rd trade)
            create_test_signal("HDFC"),  # Should be rejected (4th trade)
        ]
        
        executed_orders = strategy_manager.process_signals(signals)
        
        # Should only execute 1 more order (reaching limit of 3)
        assert len(executed_orders) == 1
        assert executed_orders[0].symbol == "INFY"
        assert strategy_manager.trade_tracker.total_trades_today == 3
        
    def test_strategy_manager_square_off_time_check(self, strategy_manager):
        """Test strategy manager respects square-off time"""
        with patch('py_eq.strategies.production_strategy_manager.datetime') as mock_datetime:
            # Mock time to be after square-off time (15:12)
            mock_datetime.now.return_value.time.return_value = time(15, 30)
            
            signals = [create_test_signal("RELIANCE")]
            executed_orders = strategy_manager.process_signals(signals)
            
            # Should not execute any orders after square-off time
            assert len(executed_orders) == 0


class TestOrderPlacementPrevention:
    """Test prevention of excessive order placement"""
    
    def test_order_service_integration(self, strategy_manager):
        """Test that order service is called exactly the right number of times"""
        order_service = strategy_manager.order_service
        
        # Create 10 signals but should only place 3 orders
        signals = [create_test_signal(f"STOCK_{i}") for i in range(10)]
        
        executed_orders = strategy_manager.process_signals(signals)
        
        # Verify only 3 orders were placed
        assert len(executed_orders) == 3
        assert len(order_service.orders_placed) == 3
        
        # Verify no more orders can be placed
        more_signals = [create_test_signal("EXTRA_STOCK")]
        more_orders = strategy_manager.process_signals(more_signals)
        assert len(more_orders) == 0
        assert len(order_service.orders_placed) == 3  # Still only 3
        
    def test_failed_order_doesnt_count_as_trade(self, strategy_manager):
        """Test that failed orders don't count towards trade limit"""
        order_service = strategy_manager.order_service
        
        # Make first order fail
        order_service.should_fail = True
        
        signals = [
            create_test_signal("RELIANCE"),  # This will fail
            create_test_signal("TCS"),       # This will fail
        ]
        
        executed_orders = strategy_manager.process_signals(signals)
        
        # No orders should be executed due to failures
        assert len(executed_orders) == 0
        assert strategy_manager.trade_tracker.total_trades_today == 0
        
        # Now allow orders to succeed
        order_service.should_fail = False
        
        # Should still be able to place 3 trades
        more_signals = [create_test_signal(f"STOCK_{i}") for i in range(5)]
        more_orders = strategy_manager.process_signals(more_signals)
        
        assert len(more_orders) == 3  # Should place 3 successful orders


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
