"""
Test the fixes for entry_price validation and trade limit enforcement
"""
import pytest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Add the parent directory to the path so we can import our modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from models.order import Order, TransactionType, OrderType, ProductType, OrderStatus
from services.order_service import SmartAPIOrderService
from strategies.production_strategy_manager import ProductionStrategyManager, TradeTracker


class TestEntryPriceValidationFix:
    """Test that positions with invalid entry prices are properly handled"""

    def test_timing_issue_fix_with_order_book_lookup(self):
        """Test that the system can fetch executed price from order book when averageprice is not immediately available"""
        # Mock logger
        mock_logger = Mock()

        # Mock SmartAPI config
        mock_config = Mock()
        mock_config.api_key = "test_key"
        mock_config.username = "test_user"
        mock_config.password = "test_pass"
        mock_config.totp_token = "test_token"

        # Create order service
        order_service = SmartAPIOrderService(mock_config, mock_logger)

        # Mock the smart_api object
        order_service.smart_api = Mock()
        order_service._authenticated = True

        # Mock order book response with executed price
        mock_order_book_response = {
            'status': True,
            'data': [
                {
                    'orderid': 'ORDER123',
                    'averageprice': '2500.75',  # Executed price available in order book
                    'status': 'COMPLETE'
                }
            ]
        }
        order_service.smart_api.orderBook.return_value = mock_order_book_response

        # Test the helper method
        executed_price = order_service._get_executed_price_from_order_book('ORDER123', 'RELIANCE')

        assert executed_price == 2500.75
        assert order_service.smart_api.orderBook.called

        print("✓ Order book lookup timing fix test PASSED")

    def test_invalid_entry_price_positions_are_skipped(self):
        """Test that positions with zero or invalid average prices are skipped"""
        # Mock logger
        mock_logger = Mock()
        
        # Mock SmartAPI config
        mock_config = Mock()
        mock_config.api_key = "test_key"
        mock_config.username = "test_user"
        mock_config.password = "test_pass"
        mock_config.totp_token = "test_token"
        
        # Create order service
        order_service = SmartAPIOrderService(mock_config, mock_logger)
        
        # Mock the get_real_positions method to return positions with invalid prices
        mock_positions = [
            {
                'netqty': '10',
                'tradingsymbol': 'RELIANCE-EQ',
                'symboltoken': '12345',
                'exchange': 'NSE',
                'orderid': 'ORDER1',
                'averageprice': 0  # Invalid price
            },
            {
                'netqty': '5',
                'tradingsymbol': 'TCS-EQ',
                'symboltoken': '67890',
                'exchange': 'NSE',
                'orderid': 'ORDER2',
                'averageprice': None  # Invalid price
            },
            {
                'netqty': '15',
                'tradingsymbol': 'INFY-EQ',
                'symboltoken': '11111',
                'exchange': 'NSE',
                'orderid': 'ORDER3',
                'averageprice': ''  # Invalid price
            },
            {
                'netqty': '20',
                'tradingsymbol': 'WIPRO-EQ',
                'symboltoken': '22222',
                'exchange': 'NSE',
                'orderid': 'ORDER4',
                'averageprice': '2500.50'  # Valid price
            }
        ]
        
        order_service.get_real_positions = Mock(return_value=mock_positions)
        
        # Call get_open_positions which should filter out invalid positions
        orders = order_service.get_open_positions()
        
        # Should only return 1 order (the one with valid price)
        assert len(orders) == 1
        assert orders[0].symbol == 'WIPRO'
        assert orders[0].price == 2500.50
        assert orders[0].entry_price == 2500.50
        
        # Check that warnings were logged for invalid positions
        warning_calls = [call for call in mock_logger.warning.call_args_list 
                        if "Skipping position" in str(call)]
        assert len(warning_calls) == 3  # Should warn for 3 invalid positions
        
        print("✓ Entry price validation fix test PASSED")

    def test_position_conversion_with_order_book_fallback(self):
        """Test that positions with missing averageprice can be recovered from order book"""
        # Mock logger
        mock_logger = Mock()

        # Mock SmartAPI config
        mock_config = Mock()
        mock_config.api_key = "test_key"
        mock_config.username = "test_user"
        mock_config.password = "test_pass"
        mock_config.totp_token = "test_token"

        # Create order service
        order_service = SmartAPIOrderService(mock_config, mock_logger)
        order_service.smart_api = Mock()
        order_service._authenticated = True

        # Mock positions with missing averageprice
        mock_positions = [
            {
                'netqty': '10',
                'tradingsymbol': 'RELIANCE-EQ',
                'symboltoken': '12345',
                'exchange': 'NSE',
                'orderid': 'ORDER123',
                'averageprice': 0  # Missing price - should trigger order book lookup
            }
        ]

        # Mock order book response with the missing price
        mock_order_book_response = {
            'status': True,
            'data': [
                {
                    'orderid': 'ORDER123',
                    'averageprice': '2500.75',  # Price available in order book
                    'status': 'COMPLETE'
                }
            ]
        }

        order_service.get_real_positions = Mock(return_value=mock_positions)
        order_service.smart_api.orderBook.return_value = mock_order_book_response

        # Call get_open_positions which should use order book fallback
        orders = order_service.get_open_positions()

        # Should successfully create 1 order with price from order book
        assert len(orders) == 1
        assert orders[0].symbol == 'RELIANCE'
        assert orders[0].price == 2500.75
        assert orders[0].entry_price == 2500.75

        # Verify order book was called
        assert order_service.smart_api.orderBook.called

        print("✓ Position conversion with order book fallback test PASSED")


class TestTradeLimitEnforcementFix:
    """Test that trade limits are strictly enforced"""
    
    def test_trade_limit_enforcement_with_existing_positions(self):
        """Test that existing positions count toward trade limit"""
        # Mock logger
        mock_logger = Mock()
        
        # Mock market data service
        mock_market_data = Mock()
        
        # Mock order service
        mock_order_service = Mock()
        
        # Mock config
        mock_config = Mock()
        mock_config.max_trades_per_day = 3
        mock_config.max_risk_per_trade = 1000
        mock_config.square_off_time = "15:12"
        
        # Test case 1: No existing trades - should allow trading
        strategy_manager = ProductionStrategyManager(
            logger=mock_logger,
            market_data_service=mock_market_data,
            order_service=mock_order_service,
            config=mock_config,
            initial_trade_count=0,
            initial_traded_symbols=set()
        )
        
        assert strategy_manager.trade_tracker.can_trade() == True
        assert strategy_manager.trade_tracker.get_remaining_trades() == 3
        
        # Test case 2: Already at limit - should not allow trading
        strategy_manager_at_limit = ProductionStrategyManager(
            logger=mock_logger,
            market_data_service=mock_market_data,
            order_service=mock_order_service,
            config=mock_config,
            initial_trade_count=3,
            initial_traded_symbols={'STOCK1', 'STOCK2', 'STOCK3'}
        )
        
        assert strategy_manager_at_limit.trade_tracker.can_trade() == False
        assert strategy_manager_at_limit.trade_tracker.get_remaining_trades() == 0
        
        # Test case 3: Exceeding limit - should not allow trading
        strategy_manager_over_limit = ProductionStrategyManager(
            logger=mock_logger,
            market_data_service=mock_market_data,
            order_service=mock_order_service,
            config=mock_config,
            initial_trade_count=5,  # More than allowed
            initial_traded_symbols={'STOCK1', 'STOCK2', 'STOCK3', 'STOCK4', 'STOCK5'}
        )
        
        assert strategy_manager_over_limit.trade_tracker.can_trade() == False
        assert strategy_manager_over_limit.trade_tracker.get_remaining_trades() == 0
        
        print("✓ Trade limit enforcement fix test PASSED")
    
    def test_process_signals_respects_trade_limit(self):
        """Test that process_signals returns empty list when trade limit is reached"""
        # Mock logger
        mock_logger = Mock()
        
        # Mock market data service
        mock_market_data = Mock()
        
        # Mock order service
        mock_order_service = Mock()
        
        # Mock config
        mock_config = Mock()
        mock_config.max_trades_per_day = 3
        mock_config.max_risk_per_trade = 1000
        mock_config.square_off_time = "15:12"
        
        # Create strategy manager at trade limit
        strategy_manager = ProductionStrategyManager(
            logger=mock_logger,
            market_data_service=mock_market_data,
            order_service=mock_order_service,
            config=mock_config,
            initial_trade_count=3,  # Already at limit
            initial_traded_symbols={'STOCK1', 'STOCK2', 'STOCK3'}
        )
        
        # Mock signals
        mock_signals = [Mock(), Mock(), Mock()]
        
        # Process signals should return empty list
        result = strategy_manager.process_signals(mock_signals)
        assert result == []
        
        # Should log that trade limit is reached
        error_calls = [call for call in mock_logger.error.call_args_list 
                      if "CRITICAL SAFETY" in str(call)]
        assert len(error_calls) > 0
        
        print("✓ Process signals trade limit test PASSED")


def run_all_tests():
    """Run all tests"""
    print("🧪 Running trade limit and entry price validation tests...")
    print("=" * 60)
    
    try:
        # Test entry price validation fix
        test_entry_price = TestEntryPriceValidationFix()
        test_entry_price.test_timing_issue_fix_with_order_book_lookup()
        test_entry_price.test_invalid_entry_price_positions_are_skipped()
        test_entry_price.test_position_conversion_with_order_book_fallback()
        
        # Test trade limit enforcement fix
        test_trade_limit = TestTradeLimitEnforcementFix()
        test_trade_limit.test_trade_limit_enforcement_with_existing_positions()
        test_trade_limit.test_process_signals_respects_trade_limit()
        
        print("=" * 60)
        print("✅ ALL TESTS PASSED!")
        print("🔧 Both fixes are working correctly:")
        print("   1. Invalid entry prices are properly handled")
        print("   2. Trade limits are strictly enforced")
        return True
        
    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
