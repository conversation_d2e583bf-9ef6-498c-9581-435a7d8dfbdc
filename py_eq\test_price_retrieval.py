#!/usr/bin/env python3
"""
Focused test script for price retrieval and SL/target calculation
Tests the enhanced tradeBook and orderBook methods
"""

import os
import sys
import time
import logging
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.order_service import SmartAPIOrderService
from config.config import config

class PriceRetrievalTest:
    """Test class for price retrieval functionality"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.setup_logging()
        self.order_service = None
        
    def setup_logging(self):
        """Setup detailed logging for the test"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'test_price_retrieval_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
                logging.StreamHandler()
            ]
        )
        
    def initialize_order_service(self) -> bool:
        """Initialize the SmartAPI order service"""
        try:
            self.logger.info("🚀 Initializing SmartAPI Order Service...")
            self.order_service = SmartAPIOrderService(config)
            
            # Test authentication
            if self.order_service.authenticate():
                self.logger.info("✅ SmartAPI authentication successful")
                return True
            else:
                self.logger.error("❌ SmartAPI authentication failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error initializing order service: {e}")
            return False
    
    def test_trade_book_method(self):
        """Test the tradeBook method directly"""
        try:
            self.logger.info("📊 Testing tradeBook() method...")
            
            # Call tradeBook method
            trade_book_response = self.order_service.smart_api.tradeBook()
            
            if trade_book_response and trade_book_response.get('status'):
                trades = trade_book_response.get('data', [])
                self.logger.info(f"✅ TradeBook retrieved successfully with {len(trades)} trades")
                
                # Display recent trades
                if trades:
                    self.logger.info("📋 Recent trades:")
                    for i, trade in enumerate(trades[:5]):  # Show first 5 trades
                        symbol = trade.get('tradingsymbol', 'N/A')
                        avg_price = trade.get('averageprice', 'N/A')
                        quantity = trade.get('quantity', 'N/A')
                        order_id = trade.get('orderid', 'N/A')
                        
                        self.logger.info(f"   {i+1}. {symbol} - Qty: {quantity}, Avg Price: ₹{avg_price}, Order: {order_id}")
                else:
                    self.logger.info("ℹ️ No trades found in trade book")
                    
                return True
            else:
                error_msg = trade_book_response.get('message', 'Unknown error') if trade_book_response else 'No response'
                self.logger.error(f"❌ TradeBook failed: {error_msg}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error testing tradeBook method: {e}")
            return False
    
    def test_order_book_method(self):
        """Test the orderBook method directly"""
        try:
            self.logger.info("📋 Testing orderBook() method...")
            
            # Call orderBook method
            order_book_response = self.order_service.smart_api.orderBook()
            
            if order_book_response and order_book_response.get('status'):
                orders = order_book_response.get('data', [])
                self.logger.info(f"✅ OrderBook retrieved successfully with {len(orders)} orders")
                
                # Display recent orders
                if orders:
                    self.logger.info("📋 Recent orders:")
                    for i, order in enumerate(orders[:5]):  # Show first 5 orders
                        symbol = order.get('tradingsymbol', 'N/A')
                        avg_price = order.get('averageprice', 'N/A')
                        quantity = order.get('quantity', 'N/A')
                        order_id = order.get('orderid', 'N/A')
                        status = order.get('status', 'N/A')
                        
                        self.logger.info(f"   {i+1}. {symbol} - Qty: {quantity}, Avg Price: ₹{avg_price}, Status: {status}, Order: {order_id}")
                else:
                    self.logger.info("ℹ️ No orders found in order book")
                    
                return True
            else:
                error_msg = order_book_response.get('message', 'Unknown error') if order_book_response else 'No response'
                self.logger.error(f"❌ OrderBook failed: {error_msg}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error testing orderBook method: {e}")
            return False
    
    def test_position_book_method(self):
        """Test the position method to see current positions"""
        try:
            self.logger.info("💼 Testing position() method...")
            
            # Call position method
            position_response = self.order_service.smart_api.position()
            
            if position_response and position_response.get('status'):
                positions = position_response.get('data', [])
                self.logger.info(f"✅ Positions retrieved successfully with {len(positions)} positions")
                
                # Display current positions
                if positions:
                    self.logger.info("💼 Current positions:")
                    for i, pos in enumerate(positions):
                        symbol = pos.get('tradingsymbol', 'N/A')
                        avg_price = pos.get('averageprice', 'N/A')
                        net_qty = pos.get('netqty', 'N/A')
                        pnl = pos.get('unrealised', 'N/A')
                        
                        self.logger.info(f"   {i+1}. {symbol} - Qty: {net_qty}, Avg Price: ₹{avg_price}, PnL: ₹{pnl}")
                else:
                    self.logger.info("ℹ️ No open positions found")
                    
                return True
            else:
                error_msg = position_response.get('message', 'Unknown error') if position_response else 'No response'
                self.logger.error(f"❌ Position method failed: {error_msg}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error testing position method: {e}")
            return False
    
    def test_comprehensive_price_lookup(self, test_order_id: str = None):
        """Test the comprehensive price lookup method"""
        try:
            self.logger.info("🔍 Testing comprehensive price lookup...")
            
            if not test_order_id:
                # Try to get a recent order ID from order book
                order_book_response = self.order_service.smart_api.orderBook()
                if order_book_response and order_book_response.get('status'):
                    orders = order_book_response.get('data', [])
                    if orders:
                        test_order_id = orders[0].get('orderid')
                        test_symbol = orders[0].get('tradingsymbol', '').replace('-EQ', '')
                        self.logger.info(f"📋 Using recent order for test: {test_order_id} ({test_symbol})")
                    else:
                        self.logger.warning("⚠️ No orders found for testing")
                        return False
                else:
                    self.logger.error("❌ Could not get order book for testing")
                    return False
            
            if test_order_id:
                # Test comprehensive price lookup
                executed_price = self.order_service._get_executed_price_comprehensive(
                    test_order_id, test_symbol, max_retries=3
                )
                
                if executed_price:
                    self.logger.info(f"✅ Comprehensive price lookup successful: ₹{executed_price:.2f}")
                    return True
                else:
                    self.logger.warning("⚠️ Comprehensive price lookup failed")
                    return False
            else:
                self.logger.warning("⚠️ No order ID available for testing")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error testing comprehensive price lookup: {e}")
            return False
    
    def test_sl_target_calculation(self):
        """Test SL and target calculation logic"""
        try:
            self.logger.info("⚖️ Testing SL and Target calculation...")
            
            # Test with sample prices
            test_cases = [
                {"entry": 100.0, "type": "BUY", "sl_pct": 0.02, "target_pct": 0.04},
                {"entry": 200.0, "type": "SELL", "sl_pct": 0.015, "target_pct": 0.03},
                {"entry": 50.0, "type": "BUY", "sl_pct": 0.025, "target_pct": 0.05}
            ]
            
            for i, case in enumerate(test_cases, 1):
                entry_price = case["entry"]
                transaction_type = case["type"]
                sl_pct = case["sl_pct"]
                target_pct = case["target_pct"]
                
                if transaction_type == "BUY":
                    stop_loss = entry_price * (1 - sl_pct)
                    target = entry_price * (1 + target_pct)
                else:
                    stop_loss = entry_price * (1 + sl_pct)
                    target = entry_price * (1 - target_pct)
                
                risk = abs(entry_price - stop_loss)
                reward = abs(target - entry_price)
                rr_ratio = reward / risk if risk > 0 else 0
                
                self.logger.info(f"📊 Test Case {i} ({transaction_type}):")
                self.logger.info(f"   Entry: ₹{entry_price:.2f}")
                self.logger.info(f"   Stop Loss: ₹{stop_loss:.2f}")
                self.logger.info(f"   Target: ₹{target:.2f}")
                self.logger.info(f"   Risk: ₹{risk:.2f}")
                self.logger.info(f"   Reward: ₹{reward:.2f}")
                self.logger.info(f"   R:R Ratio: 1:{rr_ratio:.2f}")
                
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error testing SL/target calculation: {e}")
            return False
    
    def run_all_tests(self):
        """Run all price retrieval tests"""
        self.logger.info("🚀 Starting Price Retrieval Test Suite")
        self.logger.info("=" * 60)
        
        # Initialize order service
        if not self.initialize_order_service():
            self.logger.error("❌ Failed to initialize order service. Exiting.")
            return False
        
        # Run tests
        tests = [
            ("TradeBook Method", self.test_trade_book_method),
            ("OrderBook Method", self.test_order_book_method),
            ("Position Method", self.test_position_book_method),
            ("Comprehensive Price Lookup", self.test_comprehensive_price_lookup),
            ("SL/Target Calculation", self.test_sl_target_calculation)
        ]
        
        results = {}
        for test_name, test_func in tests:
            self.logger.info(f"\n🧪 Running {test_name}...")
            try:
                results[test_name] = test_func()
            except Exception as e:
                self.logger.error(f"❌ {test_name} failed with error: {e}")
                results[test_name] = False
        
        # Print summary
        self.logger.info("\n" + "=" * 60)
        self.logger.info("📊 TEST SUMMARY")
        self.logger.info("=" * 60)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            self.logger.info(f"{test_name}: {status}")
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        self.logger.info(f"\nOverall: {passed}/{total} tests passed")
        
        return True

def main():
    """Main function to run the test"""
    test = PriceRetrievalTest()
    
    try:
        test.run_all_tests()
    except KeyboardInterrupt:
        test.logger.info("🛑 Test interrupted by user")
    except Exception as e:
        test.logger.error(f"❌ Unexpected error: {e}")
    finally:
        test.logger.info("🏁 Test completed")

if __name__ == "__main__":
    main()
