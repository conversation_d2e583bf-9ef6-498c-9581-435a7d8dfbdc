#!/usr/bin/env python3
"""
Test script to debug position fetching issues
Compares the working fetch_open_positions.py approach with the order service
"""

import os
import sys
import logging
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.order_service import SmartAPIOrderService
from config.config import config

def setup_logging():
    """Setup detailed logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_raw_position_api(order_service):
    """Test the raw SmartAPI position() method"""
    logger = logging.getLogger(__name__)
    
    logger.info("🔍 Testing raw SmartAPI position() method...")
    
    try:
        # Call the raw position method
        positions_response = order_service.smart_api.position()
        
        if positions_response and positions_response.get('status'):
            positions = positions_response.get('data', [])
            logger.info(f"✅ Raw API returned {len(positions)} positions")
            
            # Display all positions (including zero quantity ones)
            logger.info("📋 All positions from SmartAPI:")
            for i, pos in enumerate(positions):
                symbol = pos.get('tradingsymbol', 'N/A')
                netqty = pos.get('netqty', 'N/A')
                buyavgprice = pos.get('buyavgprice', 'N/A')
                sellavgprice = pos.get('sellavgprice', 'N/A')
                averageprice = pos.get('averageprice', 'N/A')
                
                logger.info(f"   {i+1}. {symbol}: NetQty={netqty}, BuyAvg=₹{buyavgprice}, SellAvg=₹{sellavgprice}, Avg=₹{averageprice}")
            
            # Filter for non-zero positions
            open_positions = [pos for pos in positions if pos.get('netqty', '0') != '0']
            logger.info(f"📊 Found {len(open_positions)} open positions (non-zero quantity)")
            
            if open_positions:
                logger.info("💼 Open positions details:")
                for i, pos in enumerate(open_positions):
                    symbol = pos.get('tradingsymbol', 'N/A')
                    netqty = int(pos.get('netqty', 0))
                    
                    # Use the correct average price based on position type
                    if netqty > 0:  # Long position
                        avg_price = pos.get('buyavgprice', 0)
                        position_type = "LONG"
                    else:  # Short position
                        avg_price = pos.get('sellavgprice', 0)
                        position_type = "SHORT"
                    
                    logger.info(f"   {i+1}. {symbol} ({position_type}): Qty={netqty}, AvgPrice=₹{avg_price}")
            
            return open_positions
        else:
            error_msg = positions_response.get('message', 'Unknown error') if positions_response else 'No response'
            logger.error(f"❌ Raw API failed: {error_msg}")
            return []
            
    except Exception as e:
        logger.error(f"❌ Error testing raw position API: {e}")
        return []

def test_order_service_positions(order_service):
    """Test the order service get_open_positions method"""
    logger = logging.getLogger(__name__)
    
    logger.info("🔍 Testing order service get_open_positions() method...")
    
    try:
        positions = order_service.get_open_positions()
        
        logger.info(f"✅ Order service returned {len(positions)} positions")
        
        if positions:
            logger.info("📋 Positions from order service:")
            for i, order in enumerate(positions):
                logger.info(f"   {i+1}. {order.symbol}: Qty={order.quantity}, Entry=₹{order.entry_price:.2f}, SL=₹{order.stop_loss:.2f}, Target=₹{order.target:.2f}")
        
        return positions
        
    except Exception as e:
        logger.error(f"❌ Error testing order service positions: {e}")
        import traceback
        traceback.print_exc()
        return []

def compare_methods(raw_positions, order_service_positions):
    """Compare the results from both methods"""
    logger = logging.getLogger(__name__)
    
    logger.info("🔍 Comparing results from both methods...")
    
    logger.info(f"📊 Raw API: {len(raw_positions)} open positions")
    logger.info(f"📊 Order Service: {len(order_service_positions)} positions")
    
    if len(raw_positions) != len(order_service_positions):
        logger.warning("⚠️ Mismatch in number of positions between methods!")
        
        # Show what's missing
        raw_symbols = {pos.get('tradingsymbol', '').replace('-EQ', '') for pos in raw_positions}
        service_symbols = {order.symbol for order in order_service_positions}
        
        missing_in_service = raw_symbols - service_symbols
        extra_in_service = service_symbols - raw_symbols
        
        if missing_in_service:
            logger.warning(f"⚠️ Missing in order service: {missing_in_service}")
        if extra_in_service:
            logger.warning(f"⚠️ Extra in order service: {extra_in_service}")
    else:
        logger.info("✅ Both methods returned the same number of positions")

def main():
    """Main function to test position fetching"""
    logger = setup_logging()
    
    logger.info("🚀 Starting Position Fetching Debug Test")
    logger.info("=" * 60)
    
    try:
        # Initialize order service
        logger.info("📡 Initializing SmartAPI Order Service...")
        order_service = SmartAPIOrderService(config)
        
        # Authenticate
        if not order_service.authenticate():
            logger.error("❌ Authentication failed")
            return
        
        logger.info("✅ Authentication successful")
        
        # Test raw API
        logger.info("\n" + "=" * 40)
        logger.info("🧪 TEST 1: Raw SmartAPI position() method")
        logger.info("=" * 40)
        raw_positions = test_raw_position_api(order_service)
        
        # Test order service
        logger.info("\n" + "=" * 40)
        logger.info("🧪 TEST 2: Order service get_open_positions()")
        logger.info("=" * 40)
        order_service_positions = test_order_service_positions(order_service)
        
        # Compare results
        logger.info("\n" + "=" * 40)
        logger.info("🧪 TEST 3: Comparison")
        logger.info("=" * 40)
        compare_methods(raw_positions, order_service_positions)
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("📊 SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Raw API positions: {len(raw_positions)}")
        logger.info(f"Order service positions: {len(order_service_positions)}")
        
        if len(raw_positions) > 0 and len(order_service_positions) == 0:
            logger.error("❌ ISSUE: Raw API has positions but order service doesn't")
            logger.error("❌ This indicates a problem in the order service position conversion logic")
        elif len(raw_positions) == 0:
            logger.info("ℹ️ No open positions found in account")
        else:
            logger.info("✅ Position fetching appears to be working correctly")
            
    except KeyboardInterrupt:
        logger.info("🛑 Test interrupted by user")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        logger.info("🏁 Test completed")

if __name__ == "__main__":
    main()
