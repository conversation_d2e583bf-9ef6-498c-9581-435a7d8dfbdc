#!/usr/bin/env python3
"""
Test script to verify the fixes for the enhanced trading system
"""

import os
import sys
import logging

# Add the current directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from services.daily_trade_tracker import DailyTradeTracker
from services.order_state_manager import OrderStateManager, PositionRecord, PositionStatus
from services.startup_validator import StartupValidator
from models.order import Order, OrderStatus, TransactionType, OrderType, ProductType


class MockOrderServiceWithPositions:
    """Mock order service with 5 existing positions"""
    
    def __init__(self):
        # Create 5 mock positions to simulate the real scenario
        self.mock_positions = []
        symbols = ["ADANIPORTS", "CAMS", "LTTS", "MSUMI", "RBLBANK"]
        
        for i, symbol in enumerate(symbols):
            order = Order(
                order_id=f"MOCK_{symbol}_{i}",
                symbol=symbol,
                symbol_token=f"1234{i}",
                exchange="NSE",
                transaction_type=TransactionType.BUY,
                order_type=OrderType.MARKET,
                product_type=ProductType.INTRADAY,
                quantity=10 + i,
                price=100.0 + (i * 50),  # Different prices
                stop_loss=0.0,  # No stop loss set
                target=0.0,     # No target set
                status=OrderStatus.COMPLETED,
                entry_price=100.0 + (i * 50),
                strategy="EXISTING_POSITION"
            )
            self.mock_positions.append(order)
    
    def get_open_positions(self):
        return self.mock_positions
    
    def get_real_balance(self):
        return {
            'available_cash': 16.23,
            'available_margin': 0.0,
            'collateral': 0.0,
            'margin_used': 0.0
        }


def setup_logger():
    """Setup test logger"""
    logger = logging.getLogger('TestFixes')
    logger.setLevel(logging.INFO)
    
    # Console handler
    console_handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger


def test_monitoring_mode():
    """Test that the system correctly handles monitoring mode with excess positions"""
    logger = setup_logger()
    
    logger.info("🧪 Testing Enhanced System with 5 Existing Positions")
    logger.info("=" * 60)
    
    # Initialize components with mock service that has 5 positions
    mock_order_service = MockOrderServiceWithPositions()
    
    # Initialize enhanced tracking
    trade_tracker = DailyTradeTracker(
        logger=logger,
        data_dir="test_data_fixes",
        max_trades_per_day=3
    )
    
    order_manager = OrderStateManager(
        logger=logger,
        order_service=mock_order_service,
        data_dir="test_data_fixes"
    )
    
    startup_validator = StartupValidator(
        logger=logger,
        trade_tracker=trade_tracker,
        order_manager=order_manager,
        order_service=mock_order_service,
        max_trades_per_day=3
    )
    
    # Run validation
    logger.info("🔍 Running startup validation...")
    system_ready, validation_results = startup_validator.run_full_validation()
    
    # Check trading permission
    can_trade, reason = startup_validator.get_trading_permission()
    
    # Get summaries
    trade_summary = trade_tracker.get_daily_summary()
    position_summary = order_manager.get_position_summary()
    
    # Display results
    logger.info("=" * 60)
    logger.info("📊 TEST RESULTS")
    logger.info("=" * 60)
    logger.info(f"System Ready: {system_ready}")
    logger.info(f"Can Trade: {can_trade}")
    logger.info(f"Reason: {reason}")
    logger.info(f"Daily Trades: {trade_summary['executed_trades']}/{trade_summary['max_trades_per_day']}")
    logger.info(f"Open Positions: {position_summary['total_positions']}")
    logger.info(f"Position Symbols: {position_summary['symbols']}")
    
    # Verify expected behavior
    expected_behavior = {
        'should_have_5_positions': position_summary['total_positions'] == 5,
        'should_block_new_trades': not can_trade,
        'should_allow_monitoring': 'monitoring' in reason.lower() or 'excess' in reason.lower(),
        'should_have_no_executed_trades': trade_summary['executed_trades'] == 0
    }
    
    logger.info("=" * 60)
    logger.info("✅ EXPECTED BEHAVIOR VERIFICATION")
    logger.info("=" * 60)
    
    all_good = True
    for check, result in expected_behavior.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status}: {check.replace('_', ' ').title()}")
        if not result:
            all_good = False
    
    if all_good:
        logger.info("🎉 ALL CHECKS PASSED - System correctly handles excess positions!")
        logger.info("✅ System will monitor existing positions but block new trades")
        logger.info("✅ This is the correct behavior for your scenario")
    else:
        logger.error("❌ Some checks failed - system needs more fixes")
    
    # Cleanup
    import shutil
    if os.path.exists("test_data_fixes"):
        shutil.rmtree("test_data_fixes")
    
    return all_good


if __name__ == "__main__":
    success = test_monitoring_mode()
    
    if success:
        print("\n" + "=" * 80)
        print("🎯 FIXES VERIFIED SUCCESSFULLY!")
        print("=" * 80)
        print("✅ The enhanced system now correctly handles your scenario:")
        print("   • Detects 5 existing positions")
        print("   • Blocks new trades (since 5 > 3 limit)")
        print("   • Allows monitoring of existing positions")
        print("   • Will execute exit logic for running trades")
        print("   • Tomorrow will start fresh with 3-trade limit")
        print("=" * 80)
    else:
        print("\n❌ Some issues remain - check the logs above")
    
    sys.exit(0 if success else 1)
