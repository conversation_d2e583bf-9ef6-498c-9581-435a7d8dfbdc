"""
Comprehensive test runner for all trading system fixes
Run this to verify all fixes are working correctly
"""
import sys
import os
import subprocess
import logging
from datetime import datetime

# Add the parent directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def setup_logging():
    """Setup logging for test results"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'test_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def run_test_file(test_file, logger):
    """Run a specific test file and return results"""
    logger.info(f"\n{'='*60}")
    logger.info(f"Running {test_file}")
    logger.info(f"{'='*60}")
    
    try:
        # Run pytest on the specific file
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            test_file, 
            '-v',  # Verbose output
            '--tb=short',  # Short traceback format
            '--no-header',  # No header
            '--no-summary'  # No summary
        ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        logger.info(f"Exit code: {result.returncode}")
        
        if result.stdout:
            logger.info("STDOUT:")
            logger.info(result.stdout)
            
        if result.stderr:
            logger.error("STDERR:")
            logger.error(result.stderr)
            
        return result.returncode == 0, result.stdout, result.stderr
        
    except Exception as e:
        logger.error(f"Error running {test_file}: {e}")
        return False, "", str(e)

def analyze_test_results(test_results, logger):
    """Analyze test results and provide summary"""
    logger.info(f"\n{'='*60}")
    logger.info("TEST RESULTS SUMMARY")
    logger.info(f"{'='*60}")
    
    total_tests = len(test_results)
    passed_tests = sum(1 for success, _, _ in test_results.values() if success)
    failed_tests = total_tests - passed_tests
    
    logger.info(f"Total test files: {total_tests}")
    logger.info(f"Passed: {passed_tests}")
    logger.info(f"Failed: {failed_tests}")
    
    if failed_tests > 0:
        logger.error("\nFAILED TESTS:")
        for test_file, (success, stdout, stderr) in test_results.items():
            if not success:
                logger.error(f"  - {test_file}")
                if "FAILED" in stdout:
                    # Extract failed test names
                    lines = stdout.split('\n')
                    for line in lines:
                        if "FAILED" in line:
                            logger.error(f"    {line.strip()}")
    
    return failed_tests == 0

def check_critical_trade_limit_enforcement(logger):
    """Special check for trade limit enforcement - the most critical fix"""
    logger.info(f"\n{'='*60}")
    logger.info("CRITICAL CHECK: Trade Limit Enforcement")
    logger.info(f"{'='*60}")

    try:
        # Import and test the critical components directly
        # Add current directory to path to avoid import issues
        import sys
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        if parent_dir not in sys.path:
            sys.path.insert(0, parent_dir)

        from strategies.production_strategy_manager import TradeTracker
        
        # Test 1: Basic trade limit
        tracker = TradeTracker()
        assert tracker.ABSOLUTE_MAX_TRADES == 3, "CRITICAL: Absolute max trades not set to 3"
        assert tracker.max_trades_per_day == 3, "CRITICAL: Max trades per day not set to 3"
        
        # Test 2: Trade recording and limit enforcement
        for i in range(3):
            tracker.record_trade("TEST_STRATEGY", f"STOCK_{i}")
            
        assert tracker.total_trades_today == 3, "CRITICAL: Trade count not correct"
        assert not tracker.can_trade(), "CRITICAL: Should not allow more trades after 3"
        
        # Test 3: Exception on exceeding limit
        try:
            tracker.record_trade("TEST_STRATEGY", "EXCESS_STOCK")
            assert False, "CRITICAL: Should have raised exception on exceeding limit"
        except ValueError as e:
            assert "Cannot exceed 3 trades per day" in str(e), "CRITICAL: Wrong exception message"
            
        logger.info("CRITICAL CHECK PASSED: Trade limit enforcement is working correctly")
        return True

    except Exception as e:
        logger.error(f"CRITICAL CHECK FAILED: {e}")
        return False

def check_position_sizing_formula(logger):
    """Check the position sizing formula implementation"""
    logger.info(f"\n{'='*60}")
    logger.info("CHECKING: Position Sizing Formula")
    logger.info(f"{'='*60}")
    
    try:
        from services.real_balance_service import RealBalanceService
        from unittest.mock import Mock
        
        # Mock order service
        mock_order_service = Mock()
        mock_order_service.get_real_balance.return_value = {
            'available_balance': 10000.0,
            'total_balance': 12000.0,
            'margin_used': 0.0
        }
        
        # Create balance service
        balance_service = RealBalanceService(
            order_service=mock_order_service,
            logger=Mock()
        )
        
        # Test the formula: quantity = available_balance * 3.5 / share_price
        entry_price = 100.0
        stop_loss = 95.0
        quantity = balance_service.calculate_position_size(entry_price, stop_loss)
        
        expected_formula_quantity = int((10000 * 3.5) / 100)  # 350
        
        # The actual quantity might be less due to risk constraint, but should be calculated correctly
        assert quantity > 0, "Position size should be greater than 0"
        assert quantity <= expected_formula_quantity, "Position size should not exceed formula result"
        
        logger.info(f"Position sizing formula working: {quantity} shares calculated")
        logger.info(f"   Formula result: {expected_formula_quantity} shares")
        logger.info(f"   Available balance: Rs.10,000")
        logger.info(f"   Entry price: Rs.{entry_price}")

        return True

    except Exception as e:
        logger.error(f"Position sizing check failed: {e}")
        return False

def main():
    """Main test runner"""
    logger = setup_logging()
    
    logger.info("Starting comprehensive test suite for trading system fixes")
    logger.info(f"Test started at: {datetime.now()}")
    
    # List of test files to run
    test_files = [
        'test_trade_limit_enforcement.py',
        'test_position_sizing.py', 
        'test_position_validation.py'
    ]
    
    # Run all test files
    test_results = {}
    for test_file in test_files:
        if os.path.exists(test_file):
            success, stdout, stderr = run_test_file(test_file, logger)
            test_results[test_file] = (success, stdout, stderr)
        else:
            logger.warning(f"Test file not found: {test_file}")
            test_results[test_file] = (False, "", f"File not found: {test_file}")
    
    # Analyze results
    all_passed = analyze_test_results(test_results, logger)
    
    # Run critical checks
    logger.info(f"\n{'='*60}")
    logger.info("RUNNING CRITICAL SYSTEM CHECKS")
    logger.info(f"{'='*60}")
    
    trade_limit_ok = check_critical_trade_limit_enforcement(logger)
    position_sizing_ok = check_position_sizing_formula(logger)
    
    # Final summary
    logger.info(f"\n{'='*60}")
    logger.info("FINAL SUMMARY")
    logger.info(f"{'='*60}")
    
    if all_passed and trade_limit_ok and position_sizing_ok:
        logger.info("ALL TESTS PASSED! Trading system fixes are working correctly.")
        logger.info("Trade limit enforcement: WORKING")
        logger.info("Position sizing formula: WORKING")
        logger.info("Position validation: WORKING")
        logger.info("Entry price validation: WORKING")
        logger.info("Trailing stop loss: DISABLED")
        logger.info("Minimum position time: WORKING")

        logger.info("\nThe system should now:")
        logger.info("   - Limit to maximum 3 trades per day")
        logger.info("   - Calculate position size as (available_balance * 3.5) / share_price")
        logger.info("   - Enforce risk <= 1% of morning balance")
        logger.info("   - Prevent zero entry price errors")
        logger.info("   - Prevent immediate exits within 30 seconds")
        logger.info("   - Not use trailing stop loss (disabled)")

        return True
    else:
        logger.error("SOME TESTS FAILED! Please review the issues above.")
        if not trade_limit_ok:
            logger.error("CRITICAL: Trade limit enforcement has issues!")
        if not position_sizing_ok:
            logger.error("CRITICAL: Position sizing formula has issues!")
        if not all_passed:
            logger.error("Some unit tests failed!")

        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
