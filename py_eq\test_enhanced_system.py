#!/usr/bin/env python3
"""
Enhanced Trading System Test Suite
Tests the new daily trade tracking, order state management, and startup validation
"""

import os
import sys
import logging
import json
from datetime import datetime, date
from typing import Dict, List

# Add the current directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from services.daily_trade_tracker import DailyTradeTracker, TradeStatus, TradeRecord
from services.order_state_manager import OrderStateManager, PositionStatus, PositionRecord
from services.startup_validator import StartupValidator
from models.order import Order, OrderStatus, TransactionType, OrderType, ProductType


class MockOrderService:
    """Mock order service for testing"""
    
    def __init__(self):
        self.mock_positions = []
        self.mock_balance = {
            'available_cash': 10000.0,
            'available_margin': 35000.0,
            'collateral': 0.0,
            'margin_used': 0.0
        }
    
    def get_open_positions(self) -> List[Order]:
        """Return mock open positions"""
        return self.mock_positions
    
    def get_real_balance(self) -> Dict:
        """Return mock balance"""
        return self.mock_balance
    
    def add_mock_position(self, symbol: str, quantity: int, price: float):
        """Add a mock position for testing"""
        order = Order(
            order_id=f"MOCK_{symbol}_{datetime.now().strftime('%H%M%S')}",
            symbol=symbol,
            symbol_token="12345",
            exchange="NSE",
            transaction_type=TransactionType.BUY,
            order_type=OrderType.MARKET,
            product_type=ProductType.INTRADAY,
            quantity=quantity,
            price=price,
            stop_loss=price * 0.98,
            target=price * 1.02,
            status=OrderStatus.COMPLETED,
            entry_price=price,
            strategy="TEST_STRATEGY"
        )
        self.mock_positions.append(order)


def setup_test_logger() -> logging.Logger:
    """Setup test logger"""
    logger = logging.getLogger('TestEnhancedSystem')
    logger.setLevel(logging.INFO)
    
    # Console handler
    console_handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger


def test_daily_trade_tracker(logger: logging.Logger) -> bool:
    """Test daily trade tracker functionality"""
    logger.info("=" * 60)
    logger.info("🧪 Testing Daily Trade Tracker")
    logger.info("=" * 60)
    
    try:
        # Initialize tracker
        tracker = DailyTradeTracker(logger, data_dir="test_data", max_trades_per_day=3)
        
        # Test 1: Check initial state
        summary = tracker.get_daily_summary()
        assert summary['executed_trades'] == 0, "Initial executed trades should be 0"
        assert summary['can_trade_more'] == True, "Should be able to trade initially"
        logger.info("✅ Test 1 passed: Initial state correct")
        
        # Test 2: Record trade attempts
        trade_id1 = tracker.record_trade_attempt("RELIANCE", "MA_CROSSOVER")
        trade_id2 = tracker.record_trade_attempt("TCS", "SUPPORT_RESISTANCE")
        
        can_trade, reason = tracker.can_place_trade("RELIANCE")
        assert not can_trade, "Should not allow duplicate symbol trade"
        logger.info("✅ Test 2 passed: Duplicate symbol prevention works")
        
        # Test 3: Update trade status to executed
        tracker.update_trade_status(trade_id1, TradeStatus.EXECUTED, "ORDER123", 10, 2500.0)
        tracker.update_trade_status(trade_id2, TradeStatus.EXECUTED, "ORDER124", 5, 3200.0)
        
        summary = tracker.get_daily_summary()
        assert summary['executed_trades'] == 2, f"Should have 2 executed trades, got {summary['executed_trades']}"
        assert len(summary['traded_symbols']) == 2, "Should have 2 traded symbols"
        logger.info("✅ Test 3 passed: Trade execution tracking works")
        
        # Test 4: Test daily limit
        trade_id3 = tracker.record_trade_attempt("INFY", "ORB")
        tracker.update_trade_status(trade_id3, TradeStatus.EXECUTED, "ORDER125", 8, 1800.0)
        
        summary = tracker.get_daily_summary()
        assert summary['executed_trades'] == 3, "Should have 3 executed trades"
        assert not summary['can_trade_more'], "Should not be able to trade more after limit"
        
        can_trade, reason = tracker.can_place_trade("WIPRO")
        assert not can_trade, "Should not allow more trades after limit"
        logger.info("✅ Test 4 passed: Daily limit enforcement works")
        
        # Test 5: Test rejected trade
        trade_id4 = tracker.record_trade_attempt("WIPRO", "GAP_AND_GO")
        tracker.update_trade_status(trade_id4, TradeStatus.REJECTED, notes="Insufficient funds")
        
        summary = tracker.get_daily_summary()
        assert summary['rejected_trades'] == 1, "Should have 1 rejected trade"
        assert summary['executed_trades'] == 3, "Executed trades should remain 3"
        logger.info("✅ Test 5 passed: Rejected trade handling works")
        
        logger.info("🎉 Daily Trade Tracker: ALL TESTS PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Daily Trade Tracker test failed: {e}")
        return False


def test_order_state_manager(logger: logging.Logger) -> bool:
    """Test order state manager functionality"""
    logger.info("=" * 60)
    logger.info("🧪 Testing Order State Manager")
    logger.info("=" * 60)
    
    try:
        # Initialize mock order service and manager
        mock_order_service = MockOrderService()
        manager = OrderStateManager(logger, mock_order_service, data_dir="test_data")
        
        # Test 1: Check initial state
        summary = manager.get_position_summary()
        assert summary['total_positions'] == 0, "Initial positions should be 0"
        logger.info("✅ Test 1 passed: Initial state correct")
        
        # Test 2: Add mock positions to broker
        mock_order_service.add_mock_position("RELIANCE", 10, 2500.0)
        mock_order_service.add_mock_position("TCS", 5, 3200.0)
        
        # Sync with broker
        manager._sync_with_broker()
        
        summary = manager.get_position_summary()
        assert summary['total_positions'] == 2, f"Should have 2 positions, got {summary['total_positions']}"
        logger.info("✅ Test 2 passed: Broker sync works")
        
        # Test 3: Test order placement validation
        can_place, reason = manager.can_place_order("RELIANCE")
        assert not can_place, "Should not allow order for existing position"
        
        can_place, reason = manager.can_place_order("INFY")
        assert can_place, "Should allow order for new symbol"
        logger.info("✅ Test 3 passed: Order placement validation works")
        
        # Test 4: Record new order
        new_order = Order(
            order_id="ORDER126",
            symbol="INFY",
            symbol_token="12346",
            exchange="NSE",
            transaction_type=TransactionType.BUY,
            order_type=OrderType.MARKET,
            product_type=ProductType.INTRADAY,
            quantity=8,
            price=1800.0,
            stop_loss=1764.0,
            target=1836.0,
            status=OrderStatus.COMPLETED,  # Use COMPLETED instead of PARTIAL
            entry_price=1800.0,
            strategy="ORB"
        )
        
        manager.record_order_placement(new_order)
        
        summary = manager.get_position_summary()
        assert summary['total_positions'] == 3, "Should have 3 positions after recording order"
        logger.info("✅ Test 4 passed: Order recording works")
        
        # Test 5: Close position
        manager.close_position("INFY", 1820.0, "TARGET_HIT")
        
        summary = manager.get_position_summary()
        assert summary['total_positions'] == 2, "Should have 2 positions after closing one"
        logger.info("✅ Test 5 passed: Position closing works")
        
        logger.info("🎉 Order State Manager: ALL TESTS PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Order State Manager test failed: {e}")
        return False


def test_startup_validator(logger: logging.Logger) -> bool:
    """Test startup validator functionality"""
    logger.info("=" * 60)
    logger.info("🧪 Testing Startup Validator")
    logger.info("=" * 60)
    
    try:
        # Initialize components
        mock_order_service = MockOrderService()
        tracker = DailyTradeTracker(logger, data_dir="test_data", max_trades_per_day=3)
        manager = OrderStateManager(logger, mock_order_service, data_dir="test_data")
        validator = StartupValidator(logger, tracker, manager, mock_order_service, max_trades_per_day=3)
        
        # Test 1: Clean system validation (but we have trades from previous tests)
        # Reset the tracker for clean test
        tracker.force_reset_daily_count("Test reset for clean validation")

        system_ready, results = validator.run_full_validation()
        assert system_ready, "Clean system should pass validation"
        logger.info("✅ Test 1 passed: Clean system validation works")
        
        # Test 2: System with existing trades
        trade_id1 = tracker.record_trade_attempt("RELIANCE", "MA_CROSSOVER")
        tracker.update_trade_status(trade_id1, TradeStatus.EXECUTED, "ORDER123", 10, 2500.0)
        
        system_ready, results = validator.run_full_validation()
        assert system_ready, "System with 1 trade should pass validation"
        logger.info("✅ Test 2 passed: System with existing trades validation works")
        
        # Test 3: System at trade limit
        trade_id2 = tracker.record_trade_attempt("TCS", "SUPPORT_RESISTANCE")
        tracker.update_trade_status(trade_id2, TradeStatus.EXECUTED, "ORDER124", 5, 3200.0)
        trade_id3 = tracker.record_trade_attempt("INFY", "ORB")
        tracker.update_trade_status(trade_id3, TradeStatus.EXECUTED, "ORDER125", 8, 1800.0)
        
        system_ready, results = validator.run_full_validation()
        can_trade, reason = validator.get_trading_permission()
        assert not can_trade, "System at trade limit should not allow trading"
        logger.info("✅ Test 3 passed: Trade limit validation works")
        
        logger.info("🎉 Startup Validator: ALL TESTS PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Startup Validator test failed: {e}")
        return False


def cleanup_test_data():
    """Clean up test data files"""
    import shutil
    test_dir = "test_data"
    if os.path.exists(test_dir):
        shutil.rmtree(test_dir)


def main():
    """Run all tests"""
    logger = setup_test_logger()
    
    logger.info("🚀 Starting Enhanced Trading System Test Suite")
    logger.info("=" * 80)
    
    # Clean up any existing test data
    cleanup_test_data()
    
    # Run tests
    test_results = []
    
    test_results.append(test_daily_trade_tracker(logger))
    test_results.append(test_order_state_manager(logger))
    test_results.append(test_startup_validator(logger))
    
    # Summary
    logger.info("=" * 80)
    logger.info("📊 TEST SUITE SUMMARY")
    logger.info("=" * 80)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    if passed_tests == total_tests:
        logger.info(f"🎉 ALL TESTS PASSED: {passed_tests}/{total_tests}")
        logger.info("✅ Enhanced Trading System is ready for production!")
    else:
        logger.error(f"❌ SOME TESTS FAILED: {passed_tests}/{total_tests} passed")
        logger.error("🚨 System needs fixes before production use!")
    
    # Clean up test data
    cleanup_test_data()
    
    return passed_tests == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
