"""
Test position validation and entry price fixes
Tests prevention of zero entry prices and invalid position states
"""
import pytest
import logging
from unittest.mock import Mock, MagicMock, patch
from datetime import datetime, timedelta
import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from models.order import Order, TransactionType, OrderType, ProductType, OrderStatus
from services.advanced_position_monitor import AdvancedPositionMonitor
from services.position_monitor import EnhancedPositionMonitor, ExitPriority
from services.interfaces import MarketDataServiceInterface, OrderServiceInterface


class MockMarketDataService(MarketDataServiceInterface):
    def __init__(self):
        self.prices = {"RELIANCE": 2500.0, "TCS": 3500.0}
        
    def get_current_price(self, symbol: str) -> float:
        return self.prices.get(symbol, 100.0)
        
    def get_last_price(self, symbol: str) -> float:
        return self.get_current_price(symbol)
        
    def get_symbol_token(self, symbol: str):
        return ("12345", "NSE")


class MockOrderService(OrderServiceInterface):
    def __init__(self):
        self.square_off_calls = []
        
    def place_order(self, symbol, token, exchange, transaction_type, entry_price, stop_loss, target, quantity):
        return None
        
    def get_order_status(self, order_id: str):
        return OrderStatus.COMPLETED
        
    def square_off_position(self, order_id: str, exit_price: float):
        self.square_off_calls.append((order_id, exit_price))
        return True


@pytest.fixture
def mock_logger():
    return Mock(spec=logging.Logger)


class TestOrderValidation:
    """Test Order model validation"""
    
    def test_valid_order_creation(self):
        """Test creating a valid order"""
        order = Order(
            symbol="RELIANCE",
            symbol_token="12345",
            exchange="NSE",
            transaction_type=TransactionType.BUY,
            order_type=OrderType.MARKET,
            product_type=ProductType.INTRADAY,
            quantity=10,
            price=2500.0,
            stop_loss=2400.0,
            target=2600.0,
            entry_price=2500.0
        )
        
        assert order.symbol == "RELIANCE"
        assert order.entry_price == 2500.0
        assert order.price == 2500.0
        assert order.quantity == 10
        
    def test_zero_entry_price_validation(self):
        """Test that zero entry price raises ValueError"""
        with pytest.raises(ValueError, match="Invalid entry_price: 0"):
            Order(
                symbol="RELIANCE",
                symbol_token="12345",
                exchange="NSE",
                transaction_type=TransactionType.BUY,
                order_type=OrderType.MARKET,
                product_type=ProductType.INTRADAY,
                quantity=10,
                price=2500.0,
                stop_loss=2400.0,
                target=2600.0,
                entry_price=0.0  # Invalid
            )
            
    def test_negative_entry_price_validation(self):
        """Test that negative entry price raises ValueError"""
        with pytest.raises(ValueError, match="Invalid entry_price: -100"):
            Order(
                symbol="RELIANCE",
                symbol_token="12345",
                exchange="NSE",
                transaction_type=TransactionType.BUY,
                order_type=OrderType.MARKET,
                product_type=ProductType.INTRADAY,
                quantity=10,
                price=2500.0,
                stop_loss=2400.0,
                target=2600.0,
                entry_price=-100.0  # Invalid
            )
            
    def test_zero_price_validation(self):
        """Test that zero price raises ValueError"""
        with pytest.raises(ValueError, match="Invalid price: 0"):
            Order(
                symbol="RELIANCE",
                symbol_token="12345",
                exchange="NSE",
                transaction_type=TransactionType.BUY,
                order_type=OrderType.MARKET,
                product_type=ProductType.INTRADAY,
                quantity=10,
                price=0.0,  # Invalid
                stop_loss=2400.0,
                target=2600.0
            )
            
    def test_zero_quantity_validation(self):
        """Test that zero quantity raises ValueError"""
        with pytest.raises(ValueError, match="Invalid quantity: 0"):
            Order(
                symbol="RELIANCE",
                symbol_token="12345",
                exchange="NSE",
                transaction_type=TransactionType.BUY,
                order_type=OrderType.MARKET,
                product_type=ProductType.INTRADAY,
                quantity=0,  # Invalid
                price=2500.0,
                stop_loss=2400.0,
                target=2600.0
            )
            
    def test_entry_price_auto_set_from_price(self):
        """Test that entry_price is auto-set from price when None"""
        order = Order(
            symbol="RELIANCE",
            symbol_token="12345",
            exchange="NSE",
            transaction_type=TransactionType.BUY,
            order_type=OrderType.MARKET,
            product_type=ProductType.INTRADAY,
            quantity=10,
            price=2500.0,
            stop_loss=2400.0,
            target=2600.0
            # entry_price not provided
        )
        
        # Should be auto-set to price
        assert order.entry_price == 2500.0


class TestAdvancedPositionMonitorValidation:
    """Test AdvancedPositionMonitor validation"""
    
    @pytest.fixture
    def position_monitor(self, mock_logger):
        market_data_service = MockMarketDataService()
        order_service = MockOrderService()
        
        return AdvancedPositionMonitor(
            order_service=order_service,
            market_data_service=market_data_service,
            logger=mock_logger
        )
        
    def test_invalid_entry_price_handling(self, position_monitor, mock_logger):
        """Test that positions with invalid entry prices are skipped"""
        # Create order with zero entry price (this would normally be caught by Order validation,
        # but we're testing the monitor's defensive handling)
        order = Order(
            symbol="RELIANCE",
            symbol_token="12345",
            exchange="NSE",
            transaction_type=TransactionType.BUY,
            order_type=OrderType.MARKET,
            product_type=ProductType.INTRADAY,
            quantity=10,
            price=2500.0,
            stop_loss=2400.0,
            target=2600.0,
            entry_price=2500.0
        )
        
        # Manually set entry_price to invalid value to test monitor's handling
        order.entry_price = 0.0
        
        # Add position to monitor
        position_monitor.add_position(order)
        
        # Trigger price update
        position_monitor._on_price_update("RELIANCE", 2450.0)
        
        # Should log error and skip processing
        mock_logger.error.assert_called()
        error_calls = [call for call in mock_logger.error.call_args_list 
                      if "invalid entry price" in str(call)]
        assert len(error_calls) > 0
        
    def test_none_entry_price_handling(self, position_monitor, mock_logger):
        """Test that positions with None entry prices are skipped"""
        order = Order(
            symbol="RELIANCE",
            symbol_token="12345",
            exchange="NSE",
            transaction_type=TransactionType.BUY,
            order_type=OrderType.MARKET,
            product_type=ProductType.INTRADAY,
            quantity=10,
            price=2500.0,
            stop_loss=2400.0,
            target=2600.0,
            entry_price=2500.0
        )
        
        # Manually set entry_price to None
        order.entry_price = None
        
        position_monitor.add_position(order)
        position_monitor._on_price_update("RELIANCE", 2450.0)
        
        # Should log error and skip processing
        mock_logger.error.assert_called()
        error_calls = [call for call in mock_logger.error.call_args_list 
                      if "invalid entry price" in str(call)]
        assert len(error_calls) > 0


class TestMinimumPositionTime:
    """Test minimum position time to prevent immediate exits"""
    
    @pytest.fixture
    def position_monitor(self, mock_logger):
        market_data_service = MockMarketDataService()
        order_service = MockOrderService()
        
        return EnhancedPositionMonitor(
            order_service=order_service,
            market_data_service=market_data_service,
            logger=mock_logger
        )
        
    def test_immediate_exit_prevention(self, position_monitor, mock_logger):
        """Test that immediate exits are prevented within 30 seconds"""
        # Create order with recent timestamp
        order = Order(
            symbol="RELIANCE",
            symbol_token="12345",
            exchange="NSE",
            transaction_type=TransactionType.BUY,
            order_type=OrderType.MARKET,
            product_type=ProductType.INTRADAY,
            quantity=10,
            price=2500.0,
            stop_loss=2400.0,
            target=2600.0,
            entry_price=2500.0,
            placed_at=datetime.now()  # Just placed
        )
        
        # Try to execute immediate exit
        result = position_monitor._execute_immediate_exit(
            order, 2450.0, "STOP_LOSS", ExitPriority.STOP_LOSS
        )
        
        # Should be prevented
        assert result == False
        
        # Should log warning about preventing immediate exit
        mock_logger.warning.assert_called()
        warning_calls = [call for call in mock_logger.warning.call_args_list 
                        if "Preventing immediate exit" in str(call)]
        assert len(warning_calls) > 0
        
    def test_emergency_exit_allowed_immediately(self, position_monitor, mock_logger):
        """Test that emergency exits are allowed immediately"""
        order = Order(
            symbol="RELIANCE",
            symbol_token="12345",
            exchange="NSE",
            transaction_type=TransactionType.BUY,
            order_type=OrderType.MARKET,
            product_type=ProductType.INTRADAY,
            quantity=10,
            price=2500.0,
            stop_loss=2400.0,
            target=2600.0,
            entry_price=2500.0,
            placed_at=datetime.now()  # Just placed
        )
        
        # Try emergency exit - should be allowed
        with patch.object(position_monitor.order_service, 'square_off_position', return_value=True):
            result = position_monitor._execute_immediate_exit(
                order, 2450.0, "EMERGENCY_EXIT", ExitPriority.EMERGENCY
            )
            
            # Emergency exits should be allowed immediately
            assert result == True
            
    def test_square_off_time_allowed_immediately(self, position_monitor, mock_logger):
        """Test that square-off time exits are allowed immediately"""
        order = Order(
            symbol="RELIANCE",
            symbol_token="12345",
            exchange="NSE",
            transaction_type=TransactionType.BUY,
            order_type=OrderType.MARKET,
            product_type=ProductType.INTRADAY,
            quantity=10,
            price=2500.0,
            stop_loss=2400.0,
            target=2600.0,
            entry_price=2500.0,
            placed_at=datetime.now()  # Just placed
        )
        
        # Try square-off time exit - should be allowed
        with patch.object(position_monitor.order_service, 'square_off_position', return_value=True):
            result = position_monitor._execute_immediate_exit(
                order, 2450.0, "SQUARE_OFF_TIME", ExitPriority.TARGET
            )
            
            # Square-off time exits should be allowed immediately
            assert result == True
            
    def test_exit_allowed_after_minimum_time(self, position_monitor, mock_logger):
        """Test that exits are allowed after minimum time has passed"""
        # Create order with old timestamp (more than 30 seconds ago)
        old_time = datetime.now() - timedelta(seconds=35)
        order = Order(
            symbol="RELIANCE",
            symbol_token="12345",
            exchange="NSE",
            transaction_type=TransactionType.BUY,
            order_type=OrderType.MARKET,
            product_type=ProductType.INTRADAY,
            quantity=10,
            price=2500.0,
            stop_loss=2400.0,
            target=2600.0,
            entry_price=2500.0,
            placed_at=old_time
        )
        
        # Try to execute exit - should be allowed
        with patch.object(position_monitor.order_service, 'square_off_position', return_value=True):
            result = position_monitor._execute_immediate_exit(
                order, 2450.0, "STOP_LOSS", ExitPriority.STOP_LOSS
            )
            
            # Should be allowed after minimum time
            assert result == True


class TestTrailingStopLossDisabled:
    """Test that trailing stop loss is properly disabled"""
    
    @pytest.fixture
    def position_monitor(self, mock_logger):
        market_data_service = MockMarketDataService()
        order_service = MockOrderService()
        
        return AdvancedPositionMonitor(
            order_service=order_service,
            market_data_service=market_data_service,
            logger=mock_logger
        )
        
    def test_trailing_sl_disabled_by_default(self, position_monitor):
        """Test that trailing stop loss is disabled by default"""
        assert position_monitor.trailing_sl_enabled == False
        
    def test_trailing_sl_update_skipped(self, position_monitor, mock_logger):
        """Test that trailing SL updates are skipped when disabled"""
        order = Order(
            symbol="RELIANCE",
            symbol_token="12345",
            exchange="NSE",
            transaction_type=TransactionType.BUY,
            order_type=OrderType.MARKET,
            product_type=ProductType.INTRADAY,
            quantity=10,
            price=2500.0,
            stop_loss=2400.0,
            target=2600.0,
            entry_price=2500.0
        )
        
        position_monitor.add_position(order)
        
        # Trigger price update that would normally update trailing SL
        position_monitor._on_price_update("RELIANCE", 2600.0)  # Price at target
        
        # Trailing SL should not be updated (method should not be called)
        # We can verify this by checking that no trailing SL levels are set
        assert "RELIANCE" not in position_monitor.trailing_sl_levels or \
               position_monitor.trailing_sl_levels.get("RELIANCE") == order.stop_loss


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
