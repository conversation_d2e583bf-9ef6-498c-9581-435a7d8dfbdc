#!/usr/bin/env python3
"""
Simple IDEA-EQ trading test
Places a 5 quantity order and verifies price retrieval and SL/target calculation
"""

import os
import sys
import time
import logging
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.order import TransactionType
from services.order_service import SmartAPIOrderService
from config.config import config

def setup_logging():
    """Setup logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def main():
    """Main function to test IDEA-EQ trading"""
    logger = setup_logging()
    
    logger.info("🚀 Starting Simple IDEA-EQ Trading Test")
    logger.info("=" * 50)
    
    try:
        # Initialize order service
        logger.info("📡 Initializing SmartAPI Order Service...")
        order_service = SmartAPIOrderService(config)
        
        # Authenticate
        if not order_service.authenticate():
            logger.error("❌ Authentication failed")
            return
        
        logger.info("✅ Authentication successful")
        
        # Get IDEA symbol token
        logger.info("🔍 Getting IDEA symbol information...")
        symbol_token = order_service._get_symbol_token("IDEA")
        
        if not symbol_token:
            logger.error("❌ Could not find IDEA symbol token")
            return
        
        logger.info(f"✅ IDEA token found: {symbol_token}")
        
        # Get current price
        logger.info("💰 Getting current IDEA price...")
        current_price = order_service._get_current_market_price("IDEA")
        
        if not current_price:
            logger.error("❌ Could not get current IDEA price")
            return
        
        logger.info(f"✅ Current IDEA price: ₹{current_price:.2f}")
        
        # Calculate SL and target (2% SL, 4% target)
        sl_percentage = 0.02  # 2% stop loss
        target_percentage = 0.04  # 4% target
        
        stop_loss = current_price * (1 - sl_percentage)
        target = current_price * (1 + target_percentage)
        
        logger.info(f"📊 Calculated levels:")
        logger.info(f"   Entry: ₹{current_price:.2f}")
        logger.info(f"   Stop Loss: ₹{stop_loss:.2f}")
        logger.info(f"   Target: ₹{target:.2f}")
        
        # First, test position fetching before placing any orders
        logger.info("📋 Testing position fetching before placing order...")
        existing_positions = order_service.get_open_positions()
        logger.info(f"📊 Found {len(existing_positions)} existing positions")

        if existing_positions:
            logger.info("💼 Existing positions:")
            for pos in existing_positions:
                logger.info(f"   {pos.symbol}: Qty={pos.quantity}, Entry=₹{pos.entry_price:.2f}")

        # Ask for confirmation
        print(f"\n🤔 Do you want to place a BUY order for 5 quantity of IDEA-EQ?")
        print(f"   Entry: ₹{current_price:.2f}")
        print(f"   Stop Loss: ₹{stop_loss:.2f}")
        print(f"   Target: ₹{target:.2f}")
        print(f"   Risk per share: ₹{current_price - stop_loss:.2f}")
        print(f"   Total risk: ₹{(current_price - stop_loss) * 5:.2f}")

        response = input("\nEnter 'yes' to proceed, 'test' to only test position fetching: ").strip().lower()

        if response == 'test':
            logger.info("🧪 Testing position fetching only...")
            # Test raw position API
            logger.info("🔍 Testing raw SmartAPI position() method...")
            positions_response = order_service.smart_api.position()

            if positions_response and positions_response.get('status'):
                positions = positions_response.get('data', [])
                logger.info(f"✅ Raw API returned {len(positions)} total positions")

                open_positions = [pos for pos in positions if pos.get('netqty', '0') != '0']
                logger.info(f"📊 Found {len(open_positions)} open positions")

                if open_positions:
                    for i, pos in enumerate(open_positions):
                        symbol = pos.get('tradingsymbol', 'N/A')
                        netqty = int(pos.get('netqty', 0))
                        buyavg = pos.get('buyavgprice', 'N/A')
                        sellavg = pos.get('sellavgprice', 'N/A')
                        avg = pos.get('averageprice', 'N/A')

                        logger.info(f"   {i+1}. {symbol}: Qty={netqty}, BuyAvg=₹{buyavg}, SellAvg=₹{sellavg}, Avg=₹{avg}")
            return
        elif response != 'yes':
            logger.info("❌ Order cancelled by user")
            return
        
        # Place the order
        logger.info("📈 Placing BUY order for IDEA-EQ...")
        
        order = order_service.place_order(
            symbol="IDEA",
            token=symbol_token,
            exchange="NSE",
            transaction_type=TransactionType.BUY,
            entry_price=current_price,
            stop_loss=stop_loss,
            target=target,
            quantity=5
        )
        
        if order:
            logger.info("✅ Order placed successfully!")
            logger.info(f"   Order ID: {order.order_id}")
            logger.info(f"   Symbol: {order.symbol}")
            logger.info(f"   Quantity: {order.quantity}")
            logger.info(f"   Entry Price: ₹{order.entry_price:.2f}")
            logger.info(f"   Stop Loss: ₹{order.stop_loss:.2f}")
            logger.info(f"   Target: ₹{order.target:.2f}")
            
            # Wait and test price retrieval
            logger.info("⏳ Waiting 15 seconds before testing price retrieval...")
            time.sleep(15)
            
            # Test comprehensive price lookup
            logger.info("🔍 Testing comprehensive price retrieval...")
            retrieved_price = order_service._get_executed_price_comprehensive(
                order.order_id, "IDEA", max_retries=3
            )
            
            if retrieved_price:
                logger.info(f"✅ Price retrieval successful: ₹{retrieved_price:.2f}")
                
                # Check if price was updated in the order
                if abs(order.entry_price - retrieved_price) < 0.01:
                    logger.info("✅ Order entry price matches retrieved price")
                else:
                    logger.info(f"ℹ️ Price difference: Order: ₹{order.entry_price:.2f}, Retrieved: ₹{retrieved_price:.2f}")
            else:
                logger.warning("⚠️ Price retrieval failed")
            
            # Test position retrieval
            logger.info("📋 Testing position retrieval...")
            positions = order_service.get_open_positions()
            
            idea_position = None
            for pos in positions:
                if pos.symbol == "IDEA":
                    idea_position = pos
                    break
            
            if idea_position:
                logger.info("✅ IDEA position found in positions:")
                logger.info(f"   Entry Price: ₹{idea_position.entry_price:.2f}")
                logger.info(f"   Stop Loss: ₹{idea_position.stop_loss:.2f}")
                logger.info(f"   Target: ₹{idea_position.target:.2f}")
                logger.info(f"   Quantity: {idea_position.quantity}")
            else:
                logger.warning("⚠️ IDEA position not found in retrieved positions")
            
            # Summary
            logger.info("\n" + "=" * 50)
            logger.info("📊 TEST SUMMARY")
            logger.info("=" * 50)
            logger.info(f"✅ Order Placement: SUCCESS")
            logger.info(f"✅ Price Retrieval: {'SUCCESS' if retrieved_price else 'FAILED'}")
            logger.info(f"✅ Position Retrieval: {'SUCCESS' if idea_position else 'FAILED'}")
            logger.info(f"✅ SL/Target Calculation: SUCCESS")
            
        else:
            logger.error("❌ Order placement failed")
            
    except KeyboardInterrupt:
        logger.info("🛑 Test interrupted by user")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        logger.info("🏁 Test completed")

if __name__ == "__main__":
    main()
